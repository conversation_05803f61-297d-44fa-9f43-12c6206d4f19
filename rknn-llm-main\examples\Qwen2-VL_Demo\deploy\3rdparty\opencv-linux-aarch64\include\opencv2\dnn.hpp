/*M///////////////////////////////////////////////////////////////////////////////////////
//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                           License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2013, OpenCV Foundation, all rights reserved.
// Third party copyrights are property of their respective owners.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors "as is" and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
//M*/

#ifndef OPENCV_DNN_HPP
#define OPENCV_DNN_HPP

// This is an umbrella header to include into you project.
// We are free to change headers layout in dnn subfolder, so please include
// this header for future compatibility


/** @defgroup dnn Deep Neural Network module
  @{
    This module contains:
        - API for new layers creation, layers are building bricks of neural networks;
        - set of built-in most-useful Layers;
        - API to construct and modify comprehensive neural networks from layers;
        - functionality for loading serialized networks models from different frameworks.

    Functionality of this module is designed only for forward pass computations (i.e. network testing).
    A network training is in principle not supported.
  @}
*/
/** @example samples/dnn/classification.cpp
Check @ref tutorial_dnn_googlenet "the corresponding tutorial" for more details
*/
/** @example samples/dnn/colorization.cpp
*/
/** @example samples/dnn/object_detection.cpp
Check @ref tutorial_dnn_yolo "the corresponding tutorial" for more details
*/
/** @example samples/dnn/openpose.cpp
*/
/** @example samples/dnn/segmentation.cpp
*/
/** @example samples/dnn/text_detection.cpp
*/
#include <opencv2/dnn/dnn.hpp>

#endif /* OPENCV_DNN_HPP */
