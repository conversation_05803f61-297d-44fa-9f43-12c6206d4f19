import ctypes
import sys
import os
import subprocess
import resource
import threading
import time
import argparse
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify, Response
import re
from collections import deque
from difflib import SequenceMatcher

app = Flask(__name__)

# Set the dynamic library path
rkllm_lib = ctypes.CDLL('lib/librkllmrt.so')

# Define the structures from the library
RKLLM_Handle_t = ctypes.c_void_p
userdata = ctypes.c_void_p(None)

LLMCallState = ctypes.c_int
LLMCallState.RKLLM_RUN_NORMAL  = 0
LLMCallState.RKLLM_RUN_WAITING  = 1
LLMCallState.RKLLM_RUN_FINISH  = 2
LLMCallState.RKLLM_RUN_ERROR   = 3

RKLLMInputType = ctypes.c_int
RKLLMInputType.RKLLM_INPUT_PROMPT      = 0
RKLLMInputType.RKLLM_INPUT_TOKEN       = 1
RKLLMInputType.RKLLM_INPUT_EMBED       = 2
RKLLMInputType.RKLLM_INPUT_MULTIMODAL  = 3

RKLLMInferMode = ctypes.c_int
RKLLMInferMode.RKLLM_INFER_GENERATE = 0
RKLLMInferMode.RKLLM_INFER_GET_LAST_HIDDEN_LAYER = 1
RKLLMInferMode.RKLLM_INFER_GET_LOGITS = 2
class RKLLMExtendParam(ctypes.Structure):
    _fields_ = [
        ("base_domain_id", ctypes.c_int32),
        ("embed_flash", ctypes.c_int8),
        ("enabled_cpus_num", ctypes.c_int8),
        ("enabled_cpus_mask", ctypes.c_uint32),
        ("n_batch", ctypes.c_uint8),
        ("use_cross_attn", ctypes.c_int8),
        ("reserved", ctypes.c_uint8 * 104)
    ]

class RKLLMParam(ctypes.Structure):
    _fields_ = [
        ("model_path", ctypes.c_char_p),
        ("max_context_len", ctypes.c_int32),
        ("max_new_tokens", ctypes.c_int32),
        ("top_k", ctypes.c_int32),
        ("n_keep", ctypes.c_int32),
        ("top_p", ctypes.c_float),
        ("temperature", ctypes.c_float),
        ("repeat_penalty", ctypes.c_float),
        ("frequency_penalty", ctypes.c_float),
        ("presence_penalty", ctypes.c_float),
        ("mirostat", ctypes.c_int32),
        ("mirostat_tau", ctypes.c_float),
        ("mirostat_eta", ctypes.c_float),
        ("skip_special_token", ctypes.c_bool),
        ("is_async", ctypes.c_bool),
        ("img_start", ctypes.c_char_p),
        ("img_end", ctypes.c_char_p),
        ("img_content", ctypes.c_char_p),
        ("extend_param", RKLLMExtendParam),
    ]

class RKLLMLoraAdapter(ctypes.Structure):
    _fields_ = [
        ("lora_adapter_path", ctypes.c_char_p),
        ("lora_adapter_name", ctypes.c_char_p),
        ("scale", ctypes.c_float)
    ]

class RKLLMEmbedInput(ctypes.Structure):
    _fields_ = [
        ("embed", ctypes.POINTER(ctypes.c_float)),
        ("n_tokens", ctypes.c_size_t)
    ]

class RKLLMTokenInput(ctypes.Structure):
    _fields_ = [
        ("input_ids", ctypes.POINTER(ctypes.c_int32)),
        ("n_tokens", ctypes.c_size_t)
    ]

class RKLLMMultiModelInput(ctypes.Structure):
    _fields_ = [
        ("prompt", ctypes.c_char_p),
        ("image_embed", ctypes.POINTER(ctypes.c_float)),
        ("n_image_tokens", ctypes.c_size_t),
        ("n_image", ctypes.c_size_t),
        ("image_width", ctypes.c_size_t),
        ("image_height", ctypes.c_size_t)
    ]

class RKLLMInputUnion(ctypes.Union):
    _fields_ = [
        ("prompt_input", ctypes.c_char_p),
        ("embed_input", RKLLMEmbedInput),
        ("token_input", RKLLMTokenInput),
        ("multimodal_input", RKLLMMultiModelInput)
    ]

class RKLLMInput(ctypes.Structure):
    _fields_ = [
        ("role", ctypes.c_char_p),
        ("enable_thinking", ctypes.c_bool),
        ("input_type", RKLLMInputType),
        ("input_data", RKLLMInputUnion)
    ]

class RKLLMLoraParam(ctypes.Structure):
    _fields_ = [
        ("lora_adapter_name", ctypes.c_char_p)
    ]

class RKLLMPromptCacheParam(ctypes.Structure):
    _fields_ = [
        ("save_prompt_cache", ctypes.c_int),
        ("prompt_cache_path", ctypes.c_char_p)
    ]

class RKLLMInferParam(ctypes.Structure):
    _fields_ = [
        ("mode", RKLLMInferMode),
        ("lora_params", ctypes.POINTER(RKLLMLoraParam)),
        ("prompt_cache_params", ctypes.POINTER(RKLLMPromptCacheParam)),
        ("keep_history", ctypes.c_int)
    ]

class RKLLMResultLastHiddenLayer(ctypes.Structure):
    _fields_ = [
        ("hidden_states", ctypes.POINTER(ctypes.c_float)),
        ("embd_size", ctypes.c_int),
        ("num_tokens", ctypes.c_int)
    ]

class RKLLMResultLogits(ctypes.Structure):
    _fields_ = [
        ("logits", ctypes.POINTER(ctypes.c_float)),
        ("vocab_size", ctypes.c_int),
        ("num_tokens", ctypes.c_int)
    ]

class RKLLMPerfStat(ctypes.Structure):
    _fields_ = [
        ("prefill_time_ms", ctypes.c_float),
        ("prefill_tokens", ctypes.c_int),
        ("generate_time_ms", ctypes.c_float),
        ("generate_tokens", ctypes.c_int),
        ("memory_usage_mb", ctypes.c_float)
    ]

class RKLLMResult(ctypes.Structure):
    _fields_ = [
        ("text", ctypes.c_char_p),
        ("token_id", ctypes.c_int),
        ("last_hidden_layer", RKLLMResultLastHiddenLayer),
        ("logits", RKLLMResultLogits),
        ("perf", RKLLMPerfStat)
    ]

# Create a lock to control multi-user access to the server.
lock = threading.Lock()

# Create a global variable to indicate whether the server is currently in a blocked state.
is_blocking = False

# 优化的Token速率和对话统计类
class TokenMetrics:
    def __init__(self, window_size=50, stats_file="conversation_stats.json"):
        self.token_times = deque(maxlen=window_size)  # 保存最近N个token的时间戳
        self.token_count = 0  # 总token数量
        self.conversation_count = 0  # 对话次数
        self.conversation_speeds = []  # 每次对话的平均速率
        self.conversation_history = []  # 对话历史记录
        self.current_conversation_start = None  # 当前对话开始时间
        self.current_conversation_tokens = 0  # 当前对话token数量
        self.stats_file = stats_file  # 统计数据文件路径
        self.lock = threading.Lock()

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('rkllm_conversation.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # 加载历史统计数据
        self.load_stats()

    def load_stats(self):
        """从文件加载历史统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.conversation_count = data.get('conversation_count', 0)
                    self.conversation_speeds = data.get('conversation_speeds', [])
                    self.conversation_history = data.get('conversation_history', [])
                    self.token_count = data.get('total_tokens', 0)
                    self.logger.info(f"[LOAD] 加载历史统计数据: {self.conversation_count} 次对话, 总计 {self.token_count} tokens")
        except Exception as e:
            self.logger.error(f"[ERROR] 加载统计数据失败: {e}")

    def save_stats(self):
        """保存统计数据到文件"""
        try:
            data = {
                'conversation_count': self.conversation_count,
                'conversation_speeds': self.conversation_speeds,
                'conversation_history': self.conversation_history,
                'total_tokens': self.token_count,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"[SAVE] 统计数据已保存到 {self.stats_file}")
        except Exception as e:
            self.logger.error(f"[ERROR] 保存统计数据失败: {e}")

    def start_conversation(self):
        """开始新对话"""
        with self.lock:
            self.current_conversation_start = time.time()
            self.current_conversation_tokens = 0
            self.logger.info(f"[START] 开始新对话 #{self.conversation_count + 1}")

    def add_token(self):
        """添加token记录"""
        with self.lock:
            current_time = time.time()
            self.token_times.append(current_time)
            self.token_count += 1
            self.current_conversation_tokens += 1

    def end_conversation(self):
        """结束当前对话并计算统计数据"""
        with self.lock:
            if self.current_conversation_start is not None:
                conversation_duration = time.time() - self.current_conversation_start
                if conversation_duration > 0 and self.current_conversation_tokens > 0:
                    conversation_speed = self.current_conversation_tokens / conversation_duration
                    self.conversation_speeds.append(conversation_speed)
                    self.conversation_count += 1

                    # 记录对话历史
                    conversation_record = {
                        'conversation_id': self.conversation_count,
                        'timestamp': datetime.now().isoformat(),
                        'duration': conversation_duration,
                        'tokens': self.current_conversation_tokens,
                        'speed': conversation_speed
                    }
                    self.conversation_history.append(conversation_record)

                    self.logger.info(f"[END] 对话 #{self.conversation_count} 结束: {self.current_conversation_tokens} tokens, {conversation_speed:.2f} tokens/s, 耗时 {conversation_duration:.2f}s")

                    # 保存统计数据
                    self.save_stats()

                self.current_conversation_start = None
                self.current_conversation_tokens = 0

    def get_tokens_per_second(self):
        """获取当前实时token速率"""
        with self.lock:
            if len(self.token_times) < 2:
                return 0
            time_diff = self.token_times[-1] - self.token_times[0]
            if time_diff == 0:
                return 0
            return (len(self.token_times) - 1) / time_diff

    def get_average_speed(self):
        """获取全局平均token速率"""
        with self.lock:
            if not self.conversation_speeds:
                return 0
            return sum(self.conversation_speeds) / len(self.conversation_speeds)

    def get_conversation_count(self):
        """获取总对话次数"""
        with self.lock:
            return self.conversation_count

    def get_stats_summary(self):
        """获取统计摘要"""
        with self.lock:
            return {
                'total_conversations': self.conversation_count,
                'total_tokens': self.token_count,
                'average_speed': self.get_average_speed(),
                'current_speed': self.get_tokens_per_second(),
                'recent_conversations': self.conversation_history[-5:] if self.conversation_history else []
            }

class AccuracyMetrics:
    def __init__(self):
        self.total_questions = 0
        self.correct_answers = 0
        self.accuracy_history = []
        self.lock = threading.Lock()

    def add_evaluation(self, is_correct):
        with self.lock:
            self.total_questions += 1
            if is_correct:
                self.correct_answers += 1
            self.accuracy_history.append(self.get_accuracy())

    def get_accuracy(self):
        with self.lock:
            if self.total_questions == 0:
                return 0
            return self.correct_answers / self.total_questions * 100

# 初始化全局指标实例
token_metrics = TokenMetrics()
accuracy_metrics = AccuracyMetrics()

# Define global variables to store the callback function output for displaying in the Gradio interface
system_prompt = ''
global_text = []
global_state = -1
split_byte_data = bytes(b"") # Used to store the segmented byte data

recevied_messages = []

# Define the callback function
def callback_impl(result, userdata, state):
    global global_text, global_state, split_byte_data
    if state == LLMCallState.RKLLM_RUN_FINISH:
        global_state = state
        token_metrics.logger.info("[CALLBACK] 对话生成完成")
        # 结束当前对话统计
        token_metrics.end_conversation()
        print("\n")
        sys.stdout.flush()
    elif state == LLMCallState.RKLLM_RUN_ERROR:
        global_state = state
        token_metrics.logger.error("[CALLBACK] 对话生成出错")
        print("run error")
        sys.stdout.flush()
    elif state == LLMCallState.RKLLM_RUN_NORMAL:
        global_state = state
        text = result.contents.text.decode('utf-8')
        global_text += text
        # 记录token生成速率
        token_metrics.add_token()
        # 记录token生成日志（每10个token记录一次，避免日志过多）
        if token_metrics.current_conversation_tokens % 10 == 0:
            token_metrics.logger.debug(f"[TOKEN] 已生成 {token_metrics.current_conversation_tokens} tokens, 当前速率: {token_metrics.get_tokens_per_second():.2f} tokens/s")
    return 0
    

# Connect the callback function between the Python side and the C++ side
callback_type = ctypes.CFUNCTYPE(ctypes.c_int, ctypes.POINTER(RKLLMResult), ctypes.c_void_p, ctypes.c_int)
callback = callback_type(callback_impl)

# Define the RKLLM class, which includes initialization, inference, and release operations for the RKLLM model in the dynamic library
class RKLLM(object):
    def __init__(self, model_path, lora_model_path = None, prompt_cache_path = None):
        rkllm_param = RKLLMParam()
        rkllm_param.model_path = bytes(model_path, 'utf-8')

        rkllm_param.max_context_len = 4096
        rkllm_param.max_new_tokens = 4096
        rkllm_param.skip_special_token = True
        rkllm_param.n_keep = -1
        rkllm_param.top_k = 1
        rkllm_param.top_p = 0.9
        rkllm_param.temperature = 0.8
        rkllm_param.repeat_penalty = 1.1
        rkllm_param.frequency_penalty = 0.0
        rkllm_param.presence_penalty = 0.0

        rkllm_param.mirostat = 0
        rkllm_param.mirostat_tau = 5.0
        rkllm_param.mirostat_eta = 0.1

        rkllm_param.is_async = False

        rkllm_param.img_start = "".encode('utf-8')
        rkllm_param.img_end = "".encode('utf-8')
        rkllm_param.img_content = "".encode('utf-8')

        rkllm_param.extend_param.base_domain_id = 0
        rkllm_param.extend_param.embed_flash = 1
        rkllm_param.extend_param.n_batch = 1
        rkllm_param.extend_param.use_cross_attn = 0
        rkllm_param.extend_param.enabled_cpus_num = 4
        rkllm_param.extend_param.enabled_cpus_mask = (1 << 4)|(1 << 5)|(1 << 6)|(1 << 7)

        self.handle = RKLLM_Handle_t()

        self.rkllm_init = rkllm_lib.rkllm_init
        self.rkllm_init.argtypes = [ctypes.POINTER(RKLLM_Handle_t), ctypes.POINTER(RKLLMParam), callback_type]
        self.rkllm_init.restype = ctypes.c_int
        self.rkllm_init(ctypes.byref(self.handle), ctypes.byref(rkllm_param), callback)

        self.rkllm_run = rkllm_lib.rkllm_run
        self.rkllm_run.argtypes = [RKLLM_Handle_t, ctypes.POINTER(RKLLMInput), ctypes.POINTER(RKLLMInferParam), ctypes.c_void_p]
        self.rkllm_run.restype = ctypes.c_int
        
        self.set_chat_template = rkllm_lib.rkllm_set_chat_template
        self.set_chat_template.argtypes = [RKLLM_Handle_t, ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p]
        self.set_chat_template.restype = ctypes.c_int
        
        self.set_function_tools_ = rkllm_lib.rkllm_set_function_tools
        self.set_function_tools_.argtypes = [RKLLM_Handle_t, ctypes.c_char_p, ctypes.c_char_p, ctypes.c_char_p]
        self.set_function_tools_.restype = ctypes.c_int
        
        # system_prompt = "<|im_start|>system You are a helpful assistant. <|im_end|>"
        # prompt_prefix = "<|im_start|>user"
        # prompt_postfix = "<|im_end|><|im_start|>assistant"
        # self.set_chat_template(self.handle, ctypes.c_char_p(system_prompt.encode('utf-8')), ctypes.c_char_p(prompt_prefix.encode('utf-8')), ctypes.c_char_p(prompt_postfix.encode('utf-8')))

        self.rkllm_destroy = rkllm_lib.rkllm_destroy
        self.rkllm_destroy.argtypes = [RKLLM_Handle_t]
        self.rkllm_destroy.restype = ctypes.c_int
        
        self.rkllm_abort = rkllm_lib.rkllm_abort

        rkllm_lora_params = None
        if lora_model_path:
            lora_adapter_name = "test"
            lora_adapter = RKLLMLoraAdapter()
            ctypes.memset(ctypes.byref(lora_adapter), 0, ctypes.sizeof(RKLLMLoraAdapter))
            lora_adapter.lora_adapter_path = ctypes.c_char_p((lora_model_path).encode('utf-8'))
            lora_adapter.lora_adapter_name = ctypes.c_char_p((lora_adapter_name).encode('utf-8'))
            lora_adapter.scale = 1.0

            rkllm_load_lora = rkllm_lib.rkllm_load_lora
            rkllm_load_lora.argtypes = [RKLLM_Handle_t, ctypes.POINTER(RKLLMLoraAdapter)]
            rkllm_load_lora.restype = ctypes.c_int
            rkllm_load_lora(self.handle, ctypes.byref(lora_adapter))
            rkllm_lora_params = RKLLMLoraParam()
            rkllm_lora_params.lora_adapter_name = ctypes.c_char_p((lora_adapter_name).encode('utf-8'))
        
        self.rkllm_infer_params = RKLLMInferParam()
        ctypes.memset(ctypes.byref(self.rkllm_infer_params), 0, ctypes.sizeof(RKLLMInferParam))
        self.rkllm_infer_params.mode = RKLLMInferMode.RKLLM_INFER_GENERATE
        self.rkllm_infer_params.lora_params = ctypes.pointer(rkllm_lora_params) if rkllm_lora_params else None
        self.rkllm_infer_params.keep_history = 0

        self.prompt_cache_path = None
        if prompt_cache_path:
            self.prompt_cache_path = prompt_cache_path

            rkllm_load_prompt_cache = rkllm_lib.rkllm_load_prompt_cache
            rkllm_load_prompt_cache.argtypes = [RKLLM_Handle_t, ctypes.c_char_p]
            rkllm_load_prompt_cache.restype = ctypes.c_int
            rkllm_load_prompt_cache(self.handle, ctypes.c_char_p((prompt_cache_path).encode('utf-8')))
        
        self.tools = None
            
    def set_function_tools(self, system_prompt, tools, tool_response_str):
        if self.tools is None or not self.tools == tools:
            self.tools = tools
            self.set_function_tools_(self.handle, ctypes.c_char_p(system_prompt.encode('utf-8')), ctypes.c_char_p(tools.encode('utf-8')),  ctypes.c_char_p(tool_response_str.encode('utf-8')))

    def run(self, *param):
        role, enable_thinking, prompt = param
        rkllm_input = RKLLMInput()
        rkllm_input.role = role.encode('utf-8') if role is not None else "user".encode('utf-8')
        rkllm_input.enable_thinking = ctypes.c_bool(enable_thinking if enable_thinking is not None else False)
        rkllm_input.input_type = RKLLMInputType.RKLLM_INPUT_PROMPT
        rkllm_input.input_data.prompt_input = ctypes.c_char_p(prompt.encode('utf-8'))
        self.rkllm_run(self.handle, ctypes.byref(rkllm_input), ctypes.byref(self.rkllm_infer_params), None)
        return
    
    def abort(self):
        return self.rkllm_abort(self.handle)
    
    def release(self):
        self.rkllm_destroy(self.handle)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--rkllm_model_path', type=str, required=True, help='Absolute path of the converted RKLLM model on the Linux board;')
    parser.add_argument('--target_platform', type=str, required=True, help='Target platform: e.g., rk3588/rk3576;')
    parser.add_argument('--lora_model_path', type=str, help='Absolute path of the lora_model on the Linux board;')
    parser.add_argument('--prompt_cache_path', type=str, help='Absolute path of the prompt_cache file on the Linux board;')
    args = parser.parse_args()

    if not os.path.exists(args.rkllm_model_path):
        print("Error: Please provide the correct rkllm model path, and ensure it is the absolute path on the board.")
        sys.stdout.flush()
        exit()

    if not (args.target_platform in ["rk3588", "rk3576"]):
        print("Error: Please specify the correct target platform: rk3588/rk3576.")
        sys.stdout.flush()
        exit()

    if args.lora_model_path:
        if not os.path.exists(args.lora_model_path):
            print("Error: Please provide the correct lora_model path, and advise it is the absolute path on the board.")
            sys.stdout.flush()
            exit()

    if args.prompt_cache_path:
        if not os.path.exists(args.prompt_cache_path):
            print("Error: Please provide the correct prompt_cache_file path, and advise it is the absolute path on the board.")
            sys.stdout.flush()
            exit()

    # Fix frequency
    command = "sudo bash fix_freq_{}.sh".format(args.target_platform)
    subprocess.run(command, shell=True)

    # Set resource limit
    resource.setrlimit(resource.RLIMIT_NOFILE, (102400, 102400))

    # Initialize RKLLM model
    print("=========init....===========")
    sys.stdout.flush()
    model_path = args.rkllm_model_path
    rkllm_model = RKLLM(model_path, args.lora_model_path, args.prompt_cache_path)
    print("RKLLM Model has been initialized successfully！")
    print("==============================")
    sys.stdout.flush()

    # Create a function to receive data sent by the user using a request
    @app.route('/rkllm_chat', methods=['POST'])
    def receive_message():
        # Link global variables to retrieve the output information from the callback function
        global global_text, global_state, system_prompt, recevied_messages
        global is_blocking

        # If the server is in a blocking state, return a specific response.
        if is_blocking or global_state==0:
            return jsonify({'status': 'error', 'message': 'RKLLM_Server is busy! Maybe you can try again later.'}), 503
        
        lock.acquire()
        try:
            # Set the server to a blocking state.
            is_blocking = True

            # Get JSON data from the POST request.
            data = request.json
            if data and 'messages' in data:
                # Reset global variables.
                global_text = []
                global_state = -1

                # 开始新对话统计
                token_metrics.start_conversation()

                # Define the structure for the returned response.
                rkllm_responses = {
                    "id": "rkllm_chat",
                    "object": "rkllm_chat",
                    "created": None,
                    "choices": [],
                    "usage": {
                    "prompt_tokens": None,
                    "completion_tokens": None,
                    "total_tokens": None
                    }
                }

                if not "stream" in data.keys() or data["stream"] == False:
                    # Process the received data here.
                    messages = data['messages']
                    enable_thinking = data.get('enable_thinking')
                    TOOLS = data.get('tools')
                    print("Received messages:", messages)
                    
                    input_prompt = []
                    for index, message in enumerate(messages):

                        if message not in recevied_messages and TOOLS is not None:
                            recevied_messages.append(message)
                        else:
                            if index >= 1:
                                print('skip recevied_messages')
                                continue
                        
                        if message['role'] == 'system':
                            system_prompt = message['content']
                            print('skip system messages')
                            continue

                        if message['role'] == 'assistant':
                            print('skip assistant messages')
                            continue
                        
                        if message['role'] == 'tool':
                            if not isinstance(input_prompt, list):
                                input_prompt = []
                            input_prompt.append(message['content'])
                            if index < len(messages) - 1:
                                continue
                            
                        if message['role'] == 'user':
                            input_prompt = message['content']
                        elif message['role'] == 'tool':
                            input_prompt = json.dumps(input_prompt)
                            recevied_messages.clear()
                        else:
                            print("role setting error")
                            
                        rkllm_output = ""
                        
                        # Create a thread for model inference.
                        if TOOLS is not None:
                            rkllm_model.set_function_tools(system_prompt=system_prompt, tools=json.dumps(TOOLS),  tool_response_str="tool_response")
                        model_thread = threading.Thread(target=rkllm_model.run, args=(message['role'], enable_thinking, input_prompt, ))
                        model_thread.start()

                        # Wait for the model to finish running and periodically check the inference thread of the model.
                        model_thread_finished = False
                        while not model_thread_finished:
                            while len(global_text) > 0:
                                rkllm_output += global_text.pop(0)
                                time.sleep(0.005)

                            model_thread.join(timeout=0.005)
                            model_thread_finished = not model_thread.is_alive()
                    
                        rkllm_responses["choices"].append(
                            {"index": index,
                            "message": {
                                "role": "assistant",
                                "content": rkllm_output,
                                "metrics": {
                                    "tokens_per_second": token_metrics.get_tokens_per_second(),
                                    "conversation_count": token_metrics.get_conversation_count(),
                                    "average_speed": token_metrics.get_average_speed(),
                                    "total_tokens": token_metrics.token_count
                                }
                            },
                            "logprobs": None,
                            "finish_reason": "stop"
                            }
                        )
                    return jsonify(rkllm_responses), 200
                else:
                    messages = data['messages']
                    enable_thinking = data.get('enable_thinking')
                    TOOLS = data.get('tools')
                    print("Received messages:", messages)
                    
                    input_prompt = []
                    for index, message in enumerate(messages):
                        # print(recevied_messages)
                        if message not in recevied_messages and TOOLS is not None:
                            recevied_messages.append(message)
                        else:
                            if index >= 1:
                                print('skip recevied_messages')
                                continue
                        
                        if message['role'] == 'system':
                            system_prompt = message['content']
                            print('skip system messages')
                            continue

                        if message['role'] == 'assistant':
                            print('skip assistant messages')
                            continue
                        
                        if message['role'] == 'tool':
                            if not isinstance(input_prompt, list):
                                input_prompt = []
                            input_prompt.append(message['content'])
                            if index < len(messages) - 1:
                                continue
                            
                        if message['role'] == 'user':
                            input_prompt = message['content']
                        elif message['role'] == 'tool':
                            input_prompt = json.dumps(input_prompt)
                            recevied_messages.clear()
                        else:
                            print("role setting error")
                            
                        role = message.get('role')
                        rkllm_output = ""
                        
                        if TOOLS is not None:
                            rkllm_model.set_function_tools(system_prompt=system_prompt, tools=json.dumps(TOOLS),  tool_response_str="tool_response")
                        
                        def generate():
                            model_thread = threading.Thread(target=rkllm_model.run, args=(role, enable_thinking, input_prompt, ))
                            model_thread.start()

                            model_thread_finished = False
                            while not model_thread_finished:
                                while len(global_text) > 0:
                                    rkllm_output = global_text.pop(0)

                                    rkllm_responses["choices"].append(
                                        {"index": index,
                                        "delta": {
                                            "role": "assistant",
                                            "content": rkllm_output[-1],
                                            "metrics": {
                                                "tokens_per_second": token_metrics.get_tokens_per_second(),
                                                "conversation_count": token_metrics.get_conversation_count(),
                                                "average_speed": token_metrics.get_average_speed(),
                                                "total_tokens": token_metrics.token_count
                                            }
                                        },
                                        "logprobs": None,
                                        "finish_reason": "stop" if global_state == 1 else None,
                                        }
                                    )
                                    yield f"{json.dumps(rkllm_responses)}\n\n"

                                model_thread.join(timeout=0.005)
                                model_thread_finished = not model_thread.is_alive()

                    return Response(generate(), content_type='text/plain')
            else:
                return jsonify({'status': 'error', 'message': 'Invalid JSON data!'}), 400
        finally:
            lock.release()
            is_blocking = False
        
    # 添加性能统计路由
    @app.route('/metrics', methods=['GET'])
    def get_metrics():
        stats_summary = token_metrics.get_stats_summary()
        return jsonify({
            'current_tokens_per_second': token_metrics.get_tokens_per_second(),
            'average_tokens_per_second': token_metrics.get_average_speed(),
            'total_conversations': token_metrics.get_conversation_count(),
            'total_tokens': token_metrics.token_count,
            'accuracy': accuracy_metrics.get_accuracy(),
            'total_questions': accuracy_metrics.total_questions,
            'performance_stats': {
                'recent_conversations': stats_summary['recent_conversations'],
                'accuracy_history': accuracy_metrics.accuracy_history[-10:] if accuracy_metrics.accuracy_history else []
            },
            'conversation_speeds': token_metrics.conversation_speeds[-10:] if token_metrics.conversation_speeds else []
        })

    # Start the Flask application.
    # app.run(host='0.0.0.0', port=8080)
    app.run(host='0.0.0.0', port=8080, threaded=True, debug=False)

    print("====================")
    print("RKLLM model inference completed, releasing RKLLM model resources...")
    rkllm_model.release()
    print("====================")
