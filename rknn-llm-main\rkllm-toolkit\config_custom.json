{"BLOCKNAME": "CustomDecoderLayer", "TOKEN_EMBD": "embed_tokens", "ATTN_NORM": "input_layernorm", "CROSS_ATTN_NORM": "cross_layernorm", "CROSS_ATTN_Q": "cross_attn.cross_q_proj", "ATTN_Q": "", "ATTN_K": "", "ATTN_V": "", "ATTN_QKV": "self_attn.qkv_proj", "ATTN_KV": "", "KV_CONTINUOUS": "true", "ATTN_OUT": "self_attn.o_proj", "CROSS_ATTN_OUT": "cross_attn.cross_o_proj", "ATTN_POST_NORM": "", "FFN_NORM": "post_attention_layernorm", "FFN_UP": "mlp.up_proj", "FFN_GATE": "mlp.gate_proj", "ACT_TYPE": "silu", "FFN_DOWN": "mlp.down_proj", "FFN_POST_NORM": "", "OUTPUT_NORM": "norm", "OUTPUT": "lm_head"}