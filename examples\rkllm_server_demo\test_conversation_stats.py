#!/usr/bin/env python3
"""
测试对话统计功能的脚本
用于验证新增的对话次数统计和平均token速率计算功能
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加路径以便导入模块
sys.path.append(os.path.join(os.path.dirname(__file__), 'rkllm_server'))

def test_token_metrics():
    """测试TokenMetrics类的功能"""
    print("=== 测试TokenMetrics类功能 ===")
    
    # 导入TokenMetrics类（模拟导入）
    from collections import deque
    import threading
    import logging
    
    class TokenMetrics:
        def __init__(self, window_size=50, stats_file="test_conversation_stats.json"):
            self.token_times = deque(maxlen=window_size)
            self.token_count = 0
            self.conversation_count = 0
            self.conversation_speeds = []
            self.conversation_history = []
            self.current_conversation_start = None
            self.current_conversation_tokens = 0
            self.stats_file = stats_file
            self.lock = threading.Lock()
            
            # 配置日志
            logging.basicConfig(level=logging.INFO)
            self.logger = logging.getLogger(__name__)
            
            # 清理测试文件
            if os.path.exists(self.stats_file):
                os.remove(self.stats_file)
        
        def load_stats(self):
            """从文件加载历史统计数据"""
            try:
                if os.path.exists(self.stats_file):
                    with open(self.stats_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.conversation_count = data.get('conversation_count', 0)
                        self.conversation_speeds = data.get('conversation_speeds', [])
                        self.conversation_history = data.get('conversation_history', [])
                        self.token_count = data.get('total_tokens', 0)
                        self.logger.info(f"[LOAD] 加载历史统计数据: {self.conversation_count} 次对话, 总计 {self.token_count} tokens")
            except Exception as e:
                self.logger.error(f"[ERROR] 加载统计数据失败: {e}")
                
        def save_stats(self):
            """保存统计数据到文件"""
            try:
                data = {
                    'conversation_count': self.conversation_count,
                    'conversation_speeds': self.conversation_speeds,
                    'conversation_history': self.conversation_history,
                    'total_tokens': self.token_count,
                    'last_updated': datetime.now().isoformat()
                }
                with open(self.stats_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                self.logger.info(f"[SAVE] 统计数据已保存到 {self.stats_file}")
            except Exception as e:
                self.logger.error(f"[ERROR] 保存统计数据失败: {e}")
        
        def start_conversation(self):
            """开始新对话"""
            with self.lock:
                self.current_conversation_start = time.time()
                self.current_conversation_tokens = 0
                self.logger.info(f"[START] 开始新对话 #{self.conversation_count + 1}")
        
        def add_token(self):
            """添加token记录"""
            with self.lock:
                current_time = time.time()
                self.token_times.append(current_time)
                self.token_count += 1
                self.current_conversation_tokens += 1
        
        def end_conversation(self):
            """结束当前对话并计算统计数据"""
            with self.lock:
                if self.current_conversation_start is not None:
                    conversation_duration = time.time() - self.current_conversation_start
                    if conversation_duration > 0 and self.current_conversation_tokens > 0:
                        conversation_speed = self.current_conversation_tokens / conversation_duration
                        self.conversation_speeds.append(conversation_speed)
                        self.conversation_count += 1
                        
                        # 记录对话历史
                        conversation_record = {
                            'conversation_id': self.conversation_count,
                            'timestamp': datetime.now().isoformat(),
                            'duration': conversation_duration,
                            'tokens': self.current_conversation_tokens,
                            'speed': conversation_speed
                        }
                        self.conversation_history.append(conversation_record)
                        
                        self.logger.info(f"[END] 对话 #{self.conversation_count} 结束: {self.current_conversation_tokens} tokens, {conversation_speed:.2f} tokens/s, 耗时 {conversation_duration:.2f}s")
                        
                        # 保存统计数据
                        self.save_stats()
                        
                    self.current_conversation_start = None
                    self.current_conversation_tokens = 0
        
        def get_tokens_per_second(self):
            """获取当前实时token速率"""
            with self.lock:
                if len(self.token_times) < 2:
                    return 0
                time_diff = self.token_times[-1] - self.token_times[0]
                if time_diff == 0:
                    return 0
                return (len(self.token_times) - 1) / time_diff
        
        def get_average_speed(self):
            """获取全局平均token速率"""
            with self.lock:
                if not self.conversation_speeds:
                    return 0
                return sum(self.conversation_speeds) / len(self.conversation_speeds)
        
        def get_conversation_count(self):
            """获取总对话次数"""
            with self.lock:
                return self.conversation_count
        
        def get_stats_summary(self):
            """获取统计摘要"""
            with self.lock:
                return {
                    'total_conversations': self.conversation_count,
                    'total_tokens': self.token_count,
                    'average_speed': self.get_average_speed(),
                    'current_speed': self.get_tokens_per_second(),
                    'recent_conversations': self.conversation_history[-5:] if self.conversation_history else []
                }
    
    # 创建测试实例
    metrics = TokenMetrics()
    
    # 模拟3次对话
    for i in range(3):
        print(f"\n--- 模拟对话 {i+1} ---")
        metrics.start_conversation()
        
        # 模拟token生成
        for j in range(20):  # 每次对话生成20个token
            metrics.add_token()
            time.sleep(0.01)  # 模拟生成间隔
        
        metrics.end_conversation()
        
        # 显示统计信息
        stats = metrics.get_stats_summary()
        print(f"对话次数: {stats['total_conversations']}")
        print(f"总Token数: {stats['total_tokens']}")
        print(f"平均速率: {stats['average_speed']:.2f} tokens/s")
        print(f"当前速率: {stats['current_speed']:.2f} tokens/s")
    
    # 验证数据持久化
    print(f"\n--- 验证数据持久化 ---")
    if os.path.exists(metrics.stats_file):
        with open(metrics.stats_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
            print(f"保存的对话次数: {saved_data['conversation_count']}")
            print(f"保存的总Token数: {saved_data['total_tokens']}")
            print(f"保存的对话历史记录数: {len(saved_data['conversation_history'])}")
        
        # 清理测试文件
        os.remove(metrics.stats_file)
        print("测试文件已清理")
    
    print("=== TokenMetrics类功能测试完成 ===")

def test_api_response_format():
    """测试API响应格式"""
    print("\n=== 测试API响应格式 ===")
    
    # 模拟API响应数据
    mock_response = {
        "choices": [{
            "message": {
                "role": "assistant",
                "content": "这是一个测试响应",
                "metrics": {
                    "tokens_per_second": 15.5,
                    "conversation_count": 3,
                    "average_speed": 12.8,
                    "total_tokens": 150
                }
            }
        }]
    }
    
    # 验证响应格式
    if "metrics" in mock_response["choices"][0]["message"]:
        metrics = mock_response["choices"][0]["message"]["metrics"]
        print("✓ API响应包含metrics字段")
        print(f"✓ 当前Token速率: {metrics['tokens_per_second']}")
        print(f"✓ 对话次数: {metrics['conversation_count']}")
        print(f"✓ 平均速率: {metrics['average_speed']}")
        print(f"✓ 总Token数: {metrics['total_tokens']}")
    else:
        print("✗ API响应缺少metrics字段")
    
    print("=== API响应格式测试完成 ===")

def main():
    """主测试函数"""
    print("开始测试对话统计功能...")
    print(f"测试时间: {datetime.now().isoformat()}")
    
    try:
        test_token_metrics()
        test_api_response_format()
        print("\n🎉 所有测试通过！")
        return True
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
