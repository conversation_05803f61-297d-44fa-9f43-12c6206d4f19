<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="16">
            <item index="0" class="java.lang.String" itemvalue="editdistance" />
            <item index="1" class="java.lang.String" itemvalue="kenlm" />
            <item index="2" class="java.lang.String" itemvalue="torch" />
            <item index="3" class="java.lang.String" itemvalue="numpy" />
            <item index="4" class="java.lang.String" itemvalue="datasets" />
            <item index="5" class="java.lang.String" itemvalue="torchvision" />
            <item index="6" class="java.lang.String" itemvalue="tqdm" />
            <item index="7" class="java.lang.String" itemvalue="pandas" />
            <item index="8" class="java.lang.String" itemvalue="tensorboard" />
            <item index="9" class="java.lang.String" itemvalue="matplotlib" />
            <item index="10" class="java.lang.String" itemvalue="wandb" />
            <item index="11" class="java.lang.String" itemvalue="pyctcdecode" />
            <item index="12" class="java.lang.String" itemvalue="pillow" />
            <item index="13" class="java.lang.String" itemvalue="speechbrain" />
            <item index="14" class="java.lang.String" itemvalue="torchaudio" />
            <item index="15" class="java.lang.String" itemvalue="torch_audiomentations" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>