#!/usr/bin/env python3
"""
新功能测试脚本
模拟测试对话统计和平均速率功能
"""

import requests
import json
import time
import sys
import os

# 服务器配置
SERVER_URL = "http://localhost:8080"
CHAT_ENDPOINT = f"{SERVER_URL}/rkllm_chat"
METRICS_ENDPOINT = f"{SERVER_URL}/metrics"

def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    try:
        response = requests.get(f"{SERVER_URL}/metrics", timeout=5)
        if response.status_code == 200:
            print("   ✅ 服务器连接正常")
            return True
        else:
            print(f"   ❌ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 无法连接服务器: {e}")
        print("   💡 请确保服务器已启动: python rkllm_server/flask_server.py")
        return False

def test_metrics_api():
    """测试metrics API"""
    print("\n📊 测试metrics API...")
    try:
        response = requests.get(METRICS_ENDPOINT, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ metrics API响应正常")
            
            # 检查新增字段
            expected_fields = [
                'current_tokens_per_second',
                'average_tokens_per_second', 
                'total_conversations',
                'total_tokens'
            ]
            
            missing_fields = []
            for field in expected_fields:
                if field in data:
                    print(f"   ✅ {field}: {data[field]}")
                else:
                    missing_fields.append(field)
                    print(f"   ❌ 缺少字段: {field}")
            
            if missing_fields:
                print(f"   ❌ API缺少字段: {missing_fields}")
                return False
            else:
                print("   ✅ 所有新增字段都存在")
                return True
        else:
            print(f"   ❌ API响应错误: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")
        return False

def test_conversation_simulation():
    """模拟对话测试"""
    print("\n💬 模拟对话测试...")
    
    # 测试消息
    test_messages = [
        {"role": "user", "content": "你好，这是第一次对话测试"},
        {"role": "user", "content": "请介绍一下你自己"},
        {"role": "user", "content": "今天天气怎么样？"}
    ]
    
    conversation_results = []
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n   📝 测试对话 {i}...")
        
        # 准备请求数据
        data = {
            "model": "test_model",
            "messages": [message],
            "stream": False
        }
        
        try:
            # 发送请求
            start_time = time.time()
            response = requests.post(CHAT_ENDPOINT, json=data, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                response_data = response.json()
                
                # 检查响应格式
                if "choices" in response_data and len(response_data["choices"]) > 0:
                    choice = response_data["choices"][0]
                    
                    if "message" in choice and "metrics" in choice["message"]:
                        metrics = choice["message"]["metrics"]
                        
                        print(f"      ✅ 对话 {i} 成功")
                        print(f"      📊 当前速率: {metrics.get('tokens_per_second', 'N/A')} tokens/s")
                        print(f"      📊 平均速率: {metrics.get('average_speed', 'N/A')} tokens/s")
                        print(f"      📊 对话次数: {metrics.get('conversation_count', 'N/A')}")
                        print(f"      📊 总Token数: {metrics.get('total_tokens', 'N/A')}")
                        print(f"      ⏱️  响应时间: {end_time - start_time:.2f}秒")
                        
                        conversation_results.append({
                            'conversation_id': i,
                            'success': True,
                            'metrics': metrics,
                            'response_time': end_time - start_time
                        })
                    else:
                        print(f"      ❌ 对话 {i} 响应缺少metrics字段")
                        conversation_results.append({'conversation_id': i, 'success': False, 'error': 'Missing metrics'})
                else:
                    print(f"      ❌ 对话 {i} 响应格式错误")
                    conversation_results.append({'conversation_id': i, 'success': False, 'error': 'Invalid response format'})
            else:
                print(f"      ❌ 对话 {i} 请求失败: {response.status_code}")
                print(f"      错误信息: {response.text}")
                conversation_results.append({'conversation_id': i, 'success': False, 'error': f'HTTP {response.status_code}'})
        
        except Exception as e:
            print(f"      ❌ 对话 {i} 异常: {e}")
            conversation_results.append({'conversation_id': i, 'success': False, 'error': str(e)})
        
        # 等待一下再进行下一次对话
        time.sleep(2)
    
    return conversation_results

def test_data_persistence():
    """测试数据持久化"""
    print("\n💾 测试数据持久化...")
    
    stats_file = "rkllm_server/conversation_stats.json"
    
    if os.path.exists(stats_file):
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("   ✅ 统计文件存在")
            print(f"   📊 保存的对话次数: {data.get('conversation_count', 'N/A')}")
            print(f"   📊 保存的总Token数: {data.get('total_tokens', 'N/A')}")
            print(f"   📊 对话历史记录数: {len(data.get('conversation_history', []))}")
            print(f"   📊 最后更新时间: {data.get('last_updated', 'N/A')}")
            
            return True
        except Exception as e:
            print(f"   ❌ 读取统计文件失败: {e}")
            return False
    else:
        print("   ⚠️  统计文件不存在（可能还没有对话）")
        return True

def test_log_files():
    """测试日志文件"""
    print("\n📝 测试日志文件...")
    
    log_file = "rkllm_server/rkllm_conversation.log"
    
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"   ✅ 日志文件存在，共 {len(lines)} 行")
            
            # 检查最近几行日志
            if lines:
                print("   📄 最近的日志条目:")
                for line in lines[-3:]:
                    print(f"      {line.strip()}")
            
            return True
        except Exception as e:
            print(f"   ❌ 读取日志文件失败: {e}")
            return False
    else:
        print("   ⚠️  日志文件不存在（可能还没有对话）")
        return True

def generate_test_report(conversation_results):
    """生成测试报告"""
    print("\n" + "=" * 50)
    print("📋 测试报告")
    print("=" * 50)
    
    successful_conversations = [r for r in conversation_results if r.get('success', False)]
    failed_conversations = [r for r in conversation_results if not r.get('success', False)]
    
    print(f"📊 对话测试统计:")
    print(f"   总对话数: {len(conversation_results)}")
    print(f"   成功对话: {len(successful_conversations)}")
    print(f"   失败对话: {len(failed_conversations)}")
    
    if successful_conversations:
        print(f"\n✅ 成功的对话:")
        for result in successful_conversations:
            metrics = result.get('metrics', {})
            print(f"   对话 {result['conversation_id']}: "
                  f"速率 {metrics.get('tokens_per_second', 'N/A')} tokens/s, "
                  f"响应时间 {result.get('response_time', 'N/A'):.2f}s")
    
    if failed_conversations:
        print(f"\n❌ 失败的对话:")
        for result in failed_conversations:
            print(f"   对话 {result['conversation_id']}: {result.get('error', 'Unknown error')}")
    
    success_rate = len(successful_conversations) / len(conversation_results) * 100 if conversation_results else 0
    print(f"\n📈 成功率: {success_rate:.1f}%")
    
    return success_rate >= 80  # 80%以上成功率认为测试通过

def main():
    print("🧪 RKLLM新功能测试")
    print("=" * 40)
    
    # 执行测试
    tests = []
    
    # 1. 服务器连接测试
    if test_server_connection():
        tests.append(True)
        
        # 2. API测试
        tests.append(test_metrics_api())
        
        # 3. 对话模拟测试
        conversation_results = test_conversation_simulation()
        tests.append(generate_test_report(conversation_results))
        
        # 4. 数据持久化测试
        tests.append(test_data_persistence())
        
        # 5. 日志文件测试
        tests.append(test_log_files())
    else:
        tests.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    if all(tests):
        print("🎉 所有测试通过！新功能工作正常")
        return True
    else:
        print("❌ 部分测试失败，请检查上述问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
