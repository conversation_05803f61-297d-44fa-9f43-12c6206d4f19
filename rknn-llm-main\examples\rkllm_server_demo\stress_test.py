#!/usr/bin/env python3
"""
压力测试脚本
测试多次对话后统计功能的稳定性
"""

import requests
import json
import time
import threading
import sys

SERVER_URL = "http://localhost:8080"
CHAT_ENDPOINT = f"{SERVER_URL}/rkllm_chat"
METRICS_ENDPOINT = f"{SERVER_URL}/metrics"

def single_conversation(conversation_id, results):
    """单次对话测试"""
    try:
        data = {
            "model": "test_model",
            "messages": [{"role": "user", "content": f"这是第{conversation_id}次对话测试"}],
            "stream": False
        }
        
        start_time = time.time()
        response = requests.post(CHAT_ENDPOINT, json=data, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            response_data = response.json()
            if "choices" in response_data and "metrics" in response_data["choices"][0]["message"]:
                metrics = response_data["choices"][0]["message"]["metrics"]
                results[conversation_id] = {
                    'success': True,
                    'response_time': end_time - start_time,
                    'metrics': metrics
                }
            else:
                results[conversation_id] = {'success': False, 'error': 'Missing metrics'}
        else:
            results[conversation_id] = {'success': False, 'error': f'HTTP {response.status_code}'}
    
    except Exception as e:
        results[conversation_id] = {'success': False, 'error': str(e)}

def stress_test(num_conversations=10):
    """压力测试"""
    print(f"🔥 开始压力测试 - {num_conversations} 次对话")
    
    results = {}
    threads = []
    
    # 创建线程
    for i in range(num_conversations):
        thread = threading.Thread(target=single_conversation, args=(i+1, results))
        threads.append(thread)
    
    # 启动所有线程
    start_time = time.time()
    for thread in threads:
        thread.start()
        time.sleep(0.1)  # 稍微错开启动时间
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 分析结果
    successful = [r for r in results.values() if r.get('success', False)]
    failed = [r for r in results.values() if not r.get('success', False)]
    
    print(f"\n📊 压力测试结果:")
    print(f"   总对话数: {num_conversations}")
    print(f"   成功对话: {len(successful)}")
    print(f"   失败对话: {len(failed)}")
    print(f"   成功率: {len(successful)/num_conversations*100:.1f}%")
    print(f"   总耗时: {end_time - start_time:.2f}秒")
    
    if successful:
        avg_response_time = sum(r['response_time'] for r in successful) / len(successful)
        print(f"   平均响应时间: {avg_response_time:.2f}秒")
        
        # 检查对话次数是否正确递增
        final_metrics = requests.get(METRICS_ENDPOINT).json()
        print(f"   最终对话次数: {final_metrics.get('total_conversations', 'N/A')}")
        print(f"   最终总Token数: {final_metrics.get('total_tokens', 'N/A')}")
    
    return len(successful) >= num_conversations * 0.8  # 80%成功率

if __name__ == "__main__":
    num_conversations = int(sys.argv[1]) if len(sys.argv) > 1 else 5
    success = stress_test(num_conversations)
    print(f"\n{'🎉 压力测试通过!' if success else '❌ 压力测试失败!'}")
    sys.exit(0 if success else 1)
