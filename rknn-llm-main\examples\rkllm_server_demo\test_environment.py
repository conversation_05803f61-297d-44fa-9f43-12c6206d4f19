#!/usr/bin/env python3
"""
环境测试脚本
检查运行环境是否满足要求
"""

import sys
import os
import importlib

def test_python_version():
    """测试Python版本"""
    print("🐍 Python版本检查:")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ Python版本满足要求 (>=3.7)")
        return True
    else:
        print("   ❌ Python版本过低，需要3.7或更高版本")
        return False

def test_required_packages():
    """测试必需的包"""
    print("\n📦 依赖包检查:")
    required_packages = [
        'flask', 'requests', 'json', 'threading', 
        'time', 'logging', 'datetime', 'collections'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {missing_packages}")
        print("请运行: pip install flask requests")
        return False
    else:
        print("   ✅ 所有依赖包都已安装")
        return True

def test_file_structure():
    """测试文件结构"""
    print("\n📁 文件结构检查:")
    required_files = [
        "rkllm_server/flask_server.py",
        "chat_api_flask.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少文件: {missing_files}")
        return False
    else:
        print("   ✅ 所有必需文件都存在")
        return True

def main():
    print("🔧 RKLLM服务器环境测试")
    print("=" * 40)
    
    tests = [
        test_python_version(),
        test_required_packages(), 
        test_file_structure()
    ]
    
    if all(tests):
        print("\n🎉 环境测试通过！可以开始功能测试")
        return True
    else:
        print("\n❌ 环境测试失败，请修复上述问题后重试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
