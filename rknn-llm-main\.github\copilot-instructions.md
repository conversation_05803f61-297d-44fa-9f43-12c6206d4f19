# RKNN-LLM AI Agent Instructions

## 项目概述

RKNN-LLM是一个用于在瑞芯微(Rockchip)芯片上部署和运行大语言模型(LLM)的软件栈。主要组件包括:

- RKLLM-Toolkit: 在PC端进行模型转换和量化的SDK
- RKLLM Runtime: 为Rockchip NPU平台提供C/C++ API的运行时库  
- RKNPU Driver: 与NPU硬件交互的内核驱动

## 关键工作流程

### 1. 模型部署流程

1. 在PC端使用RKLLM-Toolkit转换模型为RKLLM格式
2. 将转换后的模型传输到开发板
3. 使用RKLLM C API进行推理

### 2. 服务器部署流程

两种服务器实现方式:

- Flask Server (`examples/rkllm_server_demo/rkllm_server/flask_server.py`)
- Gradio Server (`examples/rkllm_server_demo/rkllm_server/gradio_server.py`) 

部署命令示例:
```bash
# Flask
./build_rkllm_server_flask.sh --workshop /user/data --model_path /user/data/model.rkllm --platform rk3588

# Gradio 
./build_rkllm_server_gradio.sh --workshop /user/data --model_path /user/data/model.rkllm --platform rk3588
```

## 关键编程模式

### 1. RKLLM模型初始化

参考 `flask_server.py` 中的 `RKLLM` 类:

- 使用ctypes加载和调用librkllmrt.so
- 设置模型参数(上下文长度、生成参数等)
- 可选配置LoRA和prompt cache

### 2. 推理流程控制

- 使用回调函数处理模型输出
- 支持异步和流式输出
- 使用线程锁控制并发访问

### 3. 平台特定配置

运行前需要:
1. 设置CPU频率: `scripts/fix_freq_{platform}.sh`
2. 设置资源限制: `resource.setrlimit()`
3. 设置环境变量: `RKLLM_LOG_LEVEL=1` 用于性能监控

## 支持的硬件平台

- RK3588系列
- RK3576系列  
- RK3562系列
- RV1126B系列

## 调试提示

1. 使用 `RKLLM_LOG_LEVEL=1` 环境变量查看:
   - 模型推理性能
   - 内存使用情况
   
2. 服务器API调试:
   - Flask: http://[board_ip]:8080/rkllm_chat
   - Gradio: http://[board_ip]:8080/

## 常见集成场景

1. 基础对话: 参考 `examples/DeepSeek-R1-Distill-Qwen-1.5B_Demo`
2. 多模态对话: 参考 `examples/Multimodal_Interactive_Dialogue_Demo`
3. 视觉语言模型: 参考 `examples/Qwen2-VL_Demo`
